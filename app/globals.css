@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* PATA African-inspired design system color palette */
    --background: 45 15% 97%; /* Warm off-white */
    --foreground: 25 25% 15%; /* Rich dark brown */

    --card: 45 15% 97%;
    --card-foreground: 25 25% 15%;

    --popover: 45 15% 97%;
    --popover-foreground: 25 25% 15%;

    /* Kalahari Gold as primary color */
    --primary: 45 85% 55%; /* #E6B325 equivalent */
    --primary-foreground: 25 25% 15%;

    /* Serengeti Blue as secondary color */
    --secondary: 205 65% 45%; /* #3A7CA5 equivalent */
    --secondary-foreground: 0 0% 100%;

    /* Sunset Orange as accent */
    --accent: 25 90% 60%; /* #E67E22 equivalent */
    --accent-foreground: 0 0% 100%;

    /* Baobab Green for success states */
    --success: 140 60% 35%; /* #2E8B57 equivalent */
    --success-foreground: 0 0% 100%;

    /* Sahara Sand for muted elements */
    --muted: 40 25% 85%; /* #D4C4A8 equivalent */
    --muted-foreground: 25 25% 35%;

    /* A<PERSON> Brown for borders */
    --border: 30 20% 75%; /* #B8A082 equivalent */
    --input: 30 20% 75%;
    --ring: 45 85% 55%;

    /* Terracotta for destructive actions */
    --destructive: 15 75% 55%; /* #CD5C5C equivalent */
    --destructive-foreground: 0 0% 100%;

    /* Additional African-inspired colors */
    --earth-red: 15 75% 55%; /* Terracotta */
    --earth-red-foreground: 0 0% 100%;

    --savanna-yellow: 50 90% 70%; /* Bright savanna yellow */
    --savanna-yellow-foreground: 25 25% 15%;

    --tribal-purple: 280 60% 40%; /* Deep tribal purple */
    --tribal-purple-foreground: 0 0% 100%;

    --radius: 0.75rem; /* Slightly more rounded for African aesthetic */
  }

  .dark {
    --background: 270 30% 10%;
    --foreground: 240 30% 98%;

    --card: 270 30% 12%;
    --card-foreground: 240 30% 98%;

    --popover: 270 30% 12%;
    --popover-foreground: 240 30% 98%;

    --primary: 270 100% 30%;
    --primary-foreground: 240 30% 98%;

    --secondary: 16 100% 50%;
    --secondary-foreground: 0 0% 100%;

    --accent: 50 100% 50%;
    --accent-foreground: 270 30% 10%;

    --success: 120 100% 25%;
    --success-foreground: 0 0% 100%;

    --info: 210 80% 55%;
    --info-foreground: 0 0% 100%;

    --magenta: 300 100% 50%;
    --magenta-foreground: 0 0% 100%;

    --muted: 270 20% 20%;
    --muted-foreground: 270 10% 70%;

    --destructive: 0 62% 30%;
    --destructive-foreground: 240 30% 98%;

    --border: 270 30% 30%;
    --input: 270 30% 30%;
    --ring: 270 100% 30%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.font-pata {
  font-family: "Montserrat", sans-serif;
}

/* Game-inspired UI elements */
.game-card {
  @apply relative overflow-hidden rounded-xl border-2 border-primary/50 bg-gradient-to-br from-background to-muted shadow-lg;
  box-shadow: 0 0 15px rgba(75, 0, 130, 0.2);
}

.game-button {
  @apply relative overflow-hidden rounded-xl bg-gradient-to-r from-primary to-primary/80 text-primary-foreground shadow-md transition-all hover:shadow-lg hover:from-primary/90 hover:to-primary active:scale-95;
}

.game-button-secondary {
  @apply relative overflow-hidden rounded-xl bg-gradient-to-r from-secondary to-secondary/80 text-secondary-foreground shadow-md transition-all hover:shadow-lg hover:from-secondary/90 hover:to-secondary active:scale-95;
}

.game-button-accent {
  @apply relative overflow-hidden rounded-xl bg-gradient-to-r from-accent to-accent/80 text-accent-foreground shadow-md transition-all hover:shadow-lg hover:from-accent/90 hover:to-accent active:scale-95;
}

.game-input {
  @apply rounded-xl border-2 border-primary/30 bg-background/80 focus:border-primary focus:ring-2 focus:ring-primary/30;
}

.glow-effect {
  position: relative;
}

.glow-effect::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #4b0082, #ff4500);
  z-index: -1;
  filter: blur(15px);
  opacity: 0.5;
  border-radius: inherit;
}

.glow-effect-gold {
  position: relative;
}

.glow-effect-gold::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ffd700, #ff4500);
  z-index: -1;
  filter: blur(15px);
  opacity: 0.5;
  border-radius: inherit;
}

.glow-effect-blue {
  position: relative;
}

.glow-effect-blue::after {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #1e90ff, #ff00ff);
  z-index: -1;
  filter: blur(15px);
  opacity: 0.5;
  border-radius: inherit;
}

/* Custom scrollbar for game feel */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}

/* Game-inspired animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

@keyframes pulse-glow {
  0% {
    box-shadow: 0 0 5px 0 rgba(75, 0, 130, 0.5);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(75, 0, 130, 0.5);
  }
  100% {
    box-shadow: 0 0 5px 0 rgba(75, 0, 130, 0.5);
  }
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow-gold {
  0% {
    box-shadow: 0 0 5px 0 rgba(255, 215, 0, 0.5);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(255, 215, 0, 0.5);
  }
  100% {
    box-shadow: 0 0 5px 0 rgba(255, 215, 0, 0.5);
  }
}

.pulse-glow-gold {
  animation: pulse-glow-gold 2s ease-in-out infinite;
}

@keyframes pulse-glow-blue {
  0% {
    box-shadow: 0 0 5px 0 rgba(30, 144, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px 5px rgba(30, 144, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 5px 0 rgba(30, 144, 255, 0.5);
  }
}

.pulse-glow-blue {
  animation: pulse-glow-blue 2s ease-in-out infinite;
}

