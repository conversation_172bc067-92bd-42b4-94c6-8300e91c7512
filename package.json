{"name": "pata-ar-treasure-hunt", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@coinbase/onchainkit": "^0.38.13", "@coinbase/wallet-sdk": "^4.3.2", "@openzeppelin/contracts": "^4.9.3", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@rainbow-me/rainbowkit": "^2.2.5", "@react-google-maps/api": "^2.20.6", "@tanstack/react-query": "^5.76.1", "@types/jest": "^29.5.14", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@wagmi/core": "^2.17.2", "@walletconnect/ethereum-provider": "^2.20.3", "@walletconnect/universal-provider": "^2.20.3", "aframe": "^1.7.1", "ar.js": "^2.2.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eventemitter3": "^5.0.1", "framer-motion": "^12.12.1", "got": "^14.4.7", "leaflet": "^1.9.4", "lucide-react": "^0.511.0", "next": "^15.3.2", "next-themes": "^0.4.6", "pino-pretty": "^13.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-qr-code": "^2.0.15", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "viem": "^2.30.0", "wagmi": "^2.15.4"}, "devDependencies": {"@nomicfoundation/hardhat-toolbox": "^5.0.0", "@types/leaflet": "^1.9.8", "autoprefixer": "^10.4.0", "dotenv": "^16.5.0", "hardhat": "^2.24.0", "postcss": "^8.4.0", "tailwindcss": "^3.3.0"}}