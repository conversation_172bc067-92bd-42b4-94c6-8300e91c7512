{"compilerOptions": {"target": "ES6", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./*"]}, "types": ["react", "react-dom", "jest"], "plugins": [{"name": "next"}], "forceConsistentCasingInFileNames": true, "typeRoots": ["./types", "./node_modules/@types"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/types.d.ts", "types/**/*"], "exclude": ["node_modules"]}